"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { ArrowRight, Star, MessageCircle, Shield, Zap, CheckCircle } from "lucide-react"
import type { Product, Package } from "../types"

interface ProductDetailProps {
  product: Product
  onBack: () => void
  onPackageSelect: (pkg: Package) => void
  selectedPackage?: Package
}

export default function ProductDetail({ 
  product, 
  onBack, 
  onPackageSelect, 
  selectedPackage 
}: ProductDetailProps) {
  const [activePackage, setActivePackage] = useState<Package | null>(null)

  // Auto-select first package on load
  useEffect(() => {
    if (product.packages.length > 0 && !selectedPackage) {
      const firstPackage = product.packages[0]
      setActivePackage(firstPackage)
      onPackageSelect(firstPackage)
    } else if (selectedPackage) {
      setActivePackage(selectedPackage)
    }
  }, [product.packages, selectedPackage, onPackageSelect])

  const handlePackageClick = (pkg: Package) => {
    setActivePackage(pkg)
    onPackageSelect(pkg)
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-gray-900/95 backdrop-blur-md border-b border-gray-800/50">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center space-x-3 space-x-reverse">
            <button
              onClick={onBack}
              className="p-2 hover:bg-gray-800/50 rounded-lg transition-colors"
              aria-label="العودة"
            >
              <ArrowRight className="w-5 h-5" />
            </button>
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-8 h-8 rounded-lg overflow-hidden">
                <Image
                  src={product.coverImage}
                  alt={product.title}
                  width={32}
                  height={32}
                  className="w-full h-full object-cover"
                />
              </div>
              <h1 className="text-lg font-bold">{product.title}</h1>
            </div>
          </div>
        </div>
      </div>

      {/* Product Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="bg-gradient-to-br from-purple-900/30 to-pink-900/30 rounded-xl p-4 border border-purple-500/20">
          <div className="flex items-start space-x-4 space-x-reverse">
            <div className="w-16 h-16 rounded-xl overflow-hidden shadow-lg flex-shrink-0">
              <Image
                src={product.coverImage}
                alt={product.title}
                width={64}
                height={64}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-bold mb-1">{product.title}</h2>
              <p className="text-gray-300 text-sm mb-2">{product.description}</p>
              <div className="flex items-center space-x-4 space-x-reverse text-sm">
                <div className="flex items-center space-x-1 space-x-reverse">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-yellow-400 font-medium">{product.rating}</span>
                </div>
                <div className="flex items-center space-x-1 space-x-reverse text-gray-400">
                  <MessageCircle className="w-4 h-4" />
                  <span>{product.commentCount.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Trust Badges */}
          <div className="flex items-center justify-center space-x-6 space-x-reverse mt-4 pt-4 border-t border-purple-500/20">
            <div className="flex items-center space-x-2 space-x-reverse text-green-400">
              <Shield className="w-4 h-4" />
              <span className="text-sm">موزع رسمي</span>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse text-blue-400">
              <Zap className="w-4 h-4" />
              <span className="text-sm">تسليم فوري</span>
            </div>
          </div>
        </div>
      </div>

      {/* Package Selection Grid */}
      <div className="container mx-auto px-4 pb-24">
        <div className="grid grid-cols-2 gap-3">
          {product.packages.map((pkg) => {
            const isSelected = activePackage?.id === pkg.id
            const hasDiscount = pkg.discount && pkg.discount > 0
            
            return (
              <button
                key={pkg.id}
                onClick={() => handlePackageClick(pkg)}
                className={`relative p-4 rounded-xl border-2 transition-all duration-200 text-right ${
                  isSelected
                    ? "border-purple-500 bg-purple-500/10 shadow-lg shadow-purple-500/20"
                    : "border-gray-700/50 bg-gray-800/30 hover:border-gray-600 hover:bg-gray-800/50"
                }`}
              >
                {/* Discount Badge */}
                {hasDiscount && (
                  <div className="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                    -{pkg.discount}%
                  </div>
                )}

                {/* Selected Indicator */}
                {isSelected && (
                  <div className="absolute top-2 left-2">
                    <CheckCircle className="w-5 h-5 text-purple-400 fill-current" />
                  </div>
                )}

                <div className="space-y-2">
                  <h3 className="font-bold text-white">{pkg.name}</h3>
                  
                  <div className="space-y-1">
                    <div className="text-2xl font-bold text-purple-400">
                      €{pkg.price.toFixed(2)}
                    </div>
                    {hasDiscount && pkg.originalPrice && (
                      <div className="text-sm text-gray-400 line-through">
                        €{pkg.originalPrice.toFixed(2)}
                      </div>
                    )}
                  </div>

                  {pkg.description && (
                    <p className="text-xs text-gray-400">{pkg.description}</p>
                  )}

                  {/* Stock Info for Digital Codes */}
                  {pkg.hasDigitalCodes && (
                    <div className="text-xs">
                      {pkg.availableCodesCount && pkg.availableCodesCount > 0 ? (
                        <span className="text-green-400">
                          متوفر ({pkg.availableCodesCount} كود)
                        </span>
                      ) : (
                        <span className="text-red-400">نفد المخزون</span>
                      )}
                    </div>
                  )}
                </div>
              </button>
            )
          })}
        </div>
      </div>
    </div>
  )
}
