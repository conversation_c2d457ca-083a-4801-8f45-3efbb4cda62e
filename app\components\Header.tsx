"use client"

import Link from "next/link"
import Image from "next/image"
import { useState, useEffect } from "react"
import { User, Menu, X, Wallet, Shield, ChevronDown, Search } from "lucide-react"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  // TODO: Replace with actual user data from Supabase
  const mockUser = {
    name: "أحمد محمد",
    walletBalance: 150.0,
    isLoggedIn: true,
    role: "admin", // Change this to test different roles
  }

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMenuOpen && !(event.target as Element).closest('.mobile-menu-container')) {
        setIsMenuOpen(false)
      }
    }
    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [isMenuOpen])

  // Navigation items
  const navItems = [
    { href: "/", label: "الرئيسية", icon: null },
    { href: "/shop", label: "المتجر", icon: null },
    { href: "/wallet", label: "المحفظة", icon: null },
    { href: "/profile", label: "الملف الشخصي", icon: null },
  ]

  return (
    <header
      className="bg-gray-900/95 backdrop-blur-md border-b border-gray-800/50 sticky top-0 z-50 h-16"
      role="banner"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Mobile Menu Button - Left Side */}
          <button
            className="lg:hidden p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label={isMenuOpen ? "إغلاق القائمة" : "فتح القائمة"}
          >
            <Menu className="w-6 h-6 text-gray-300" />
          </button>

          {/* Logo - Left Side */}
          <Link
            href="/"
            className="flex items-center space-x-3 space-x-reverse group"
            aria-label="LDShop - الصفحة الرئيسية"
          >
            <div className="w-8 h-8 rounded-lg overflow-hidden shadow-md group-hover:shadow-lg transition-shadow duration-200">
              <div className="w-full h-full bg-gradient-to-br from-orange-500 to-purple-600 flex items-center justify-center">
                <span className="text-white font-bold text-sm">LD</span>
              </div>
            </div>
            <span className="hidden sm:block text-xl font-bold text-white">
              LDShop
            </span>
          </Link>

          {/* Search Bar - Center */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search games or goods"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg pl-10 pr-4 py-2 text-sm text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-200"
              />
            </div>
          </div>

          {/* Right Side - Language/Currency + Login */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* Language/Currency Selector */}
            <div className="hidden sm:flex items-center space-x-2 space-x-reverse text-sm text-gray-300">
              <span>EUR</span>
              <span className="text-gray-500">|</span>
              <span>English</span>
            </div>

            {/* Login Button */}
            <button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg">
              Log in
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        <div
          className={`
            lg:hidden overflow-hidden transition-all duration-300 ease-in-out
            ${isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}
          `}
          id="mobile-menu"
        >
          <div className="bg-gray-800/95 backdrop-blur-md mt-2 p-4 rounded-lg mx-4 border border-gray-700/50">
            {/* Mobile Search */}
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search games or goods"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg pl-10 pr-4 py-2 text-sm text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                />
              </div>
            </div>

            {/* Mobile Navigation */}
            <nav className="flex flex-col space-y-2">
              <Link
                href="/"
                className="text-gray-200 hover:text-white font-medium py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Home
              </Link>
              <Link
                href="/shop"
                className="text-gray-200 hover:text-white font-medium py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Game
              </Link>
              <Link
                href="/blog"
                className="text-gray-200 hover:text-white font-medium py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Blog
              </Link>
            </nav>
          </div>
        </div>
      </div>
    </header>
  )
}
