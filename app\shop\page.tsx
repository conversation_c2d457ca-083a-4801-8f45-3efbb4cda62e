"use client"

import { useState, useMemo } from "react"
import { mockProducts } from "../data/mockData"
import ProductCard from "../components/ProductCard"
import Pagination from "../components/Pagination"

export default function ShopPage() {
  const [currentPage, setCurrentPage] = useState(1)
  const productsPerPage = 20

  // TODO: Replace with actual user role from Supabase auth
  const userRole = "user"

  // Sort products by popularity
  const sortedProducts = useMemo(() => {
    return [...mockProducts].sort((a, b) => b.commentCount - a.commentCount)
  }, [])

  // Pagination logic
  const { paginatedProducts, totalPages } = useMemo(() => {
    const startIndex = (currentPage - 1) * productsPerPage
    const endIndex = startIndex + productsPerPage
    const paginatedProducts = sortedProducts.slice(startIndex, endIndex)
    const totalPages = Math.ceil(sortedProducts.length / productsPerPage)

    return { paginatedProducts, totalPages }
  }, [sortedProducts, currentPage, productsPerPage])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Products Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-8">
        {paginatedProducts.map((product) => (
          <ProductCard key={product.id} product={product} userRole={userRole} />
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          className="mt-8"
        />
      )}
    </div>
  )
}
