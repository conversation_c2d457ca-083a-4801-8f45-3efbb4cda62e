import type { Product } from "../types"
import ProductCard from "./ProductCard"

interface CategorySectionProps {
  title: string
  products: Product[]
  userRole?: "admin" | "distributor" | "user"
}

export default function CategorySection({ title, products, userRole }: CategorySectionProps) {
  if (products.length === 0) return null

  return (
    <section className="mb-12">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl sm:text-2xl font-bold text-white">
          {title}
        </h2>
        <button className="text-purple-400 hover:text-purple-300 transition-colors text-sm">
          Categories →
        </button>
      </div>

      <div className="grid grid-cols-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6 gap-3 sm:gap-4">
        {products.map((product) => (
          <ProductCard key={product.id} product={product} userRole={userRole} />
        ))}
      </div>
    </section>
  )
}
