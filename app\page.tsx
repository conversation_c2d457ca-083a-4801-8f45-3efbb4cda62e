import BannerSlider from "./components/BannerSlider"
import CategorySection from "./components/CategorySection"
import PromoBanner from "./components/PromoBanner"
import { mockProducts, mockBanners, mockHomepageSections } from "./data/mockData"

export default function HomePage() {
  // TODO: Replace with actual user role from Supabase auth
  const userRole = "user"

  // Get active homepage sections sorted by order
  const activeSections = mockHomepageSections.filter((section) => section.active).sort((a, b) => a.order - b.order)

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Promotional Banner */}
      <section className="container mx-auto px-4 pt-3">
        <PromoBanner />
      </section>

      {/* Dynamic Product Sections */}
      <div className="container mx-auto px-4 py-4">
        {activeSections.map((section) => {
          const sectionProducts = mockProducts.filter((product) => section.productIds.includes(product.id))

          if (sectionProducts.length === 0) return null

          return (
            <CategorySection
              key={section.id}
              title={`${section.emoji || ""} ${section.title}`.trim()}
              products={sectionProducts}
              userRole={userRole}
            />
          )
        })}
      </div>


    </div>
  )
}
