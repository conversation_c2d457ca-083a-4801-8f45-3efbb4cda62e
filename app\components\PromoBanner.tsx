"use client"

import Image from "next/image"
import { useState, useEffect, useRef, useCallback } from "react"

const bannerImages = [
  "/banner/1.png",
  "/banner/2.png",
  "/banner/3.png",
  "/banner/4.png",
]

export default function PromoBanner() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [currentX, setCurrentX] = useState(0)
  const [translateX, setTranslateX] = useState(0)
  const [dragProgress, setDragProgress] = useState(0) // -1 to 1, indicates drag direction and intensity
  const sliderRef = useRef<HTMLDivElement>(null)
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Auto-slide functionality
  const startAutoPlay = useCallback(() => {
    if (autoPlayRef.current) clearInterval(autoPlayRef.current)
    autoPlayRef.current = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % bannerImages.length)
    }, 4000)
  }, [])

  const stopAutoPlay = useCallback(() => {
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current)
      autoPlayRef.current = null
    }
  }, [])

  useEffect(() => {
    if (isAutoPlaying) {
      startAutoPlay()
    } else {
      stopAutoPlay()
    }
    return () => stopAutoPlay()
  }, [isAutoPlaying, startAutoPlay, stopAutoPlay])

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % bannerImages.length)
  }, [])

  const prevSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev - 1 + bannerImages.length) % bannerImages.length)
  }, [])

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index)
  }, [])

  // Touch and mouse event handlers
  const handleStart = useCallback((clientX: number) => {
    setIsDragging(true)
    setStartX(clientX)
    setCurrentX(clientX)
    setIsAutoPlaying(false)
  }, [])

  const handleMove = useCallback((clientX: number) => {
    if (!isDragging || !containerRef.current) return

    setCurrentX(clientX)
    const diff = clientX - startX
    const containerWidth = containerRef.current.offsetWidth
    const maxDrag = containerWidth * 0.3 // Limit drag to 30% of container width

    // Clamp the translation
    const clampedDiff = Math.max(-maxDrag, Math.min(maxDrag, diff))
    setTranslateX(clampedDiff)

    // Calculate drag progress (-1 to 1)
    setDragProgress(clampedDiff / maxDrag)
  }, [isDragging, startX])

  const handleEnd = useCallback(() => {
    if (!isDragging) return

    setIsDragging(false)
    const diff = currentX - startX
    const threshold = 50 // Minimum distance to trigger slide change

    if (Math.abs(diff) > threshold) {
      if (diff > 0) {
        prevSlide()
      } else {
        nextSlide()
      }
    }

    // Reset states
    setTranslateX(0)
    setDragProgress(0)
    setIsAutoPlaying(true)
  }, [isDragging, currentX, startX, nextSlide, prevSlide])

  // Mouse events
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    handleStart(e.clientX)
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    handleMove(e.clientX)
  }

  const handleMouseUp = () => {
    handleEnd()
  }

  const handleMouseLeave = () => {
    if (isDragging) {
      handleEnd()
    }
  }

  // Touch events
  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault()
    console.log('Touch start:', e.touches[0].clientX)
    handleStart(e.touches[0].clientX)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    e.preventDefault()
    console.log('Touch move:', e.touches[0].clientX)
    handleMove(e.touches[0].clientX)
  }

  const handleTouchEnd = (e: React.TouchEvent) => {
    e.preventDefault()
    console.log('Touch end')
    handleEnd()
  }

  // Pause auto-play on hover
  const handleMouseEnter = () => {
    setIsAutoPlaying(false)
  }

  const handleMouseLeaveContainer = () => {
    if (!isDragging) {
      setIsAutoPlaying(true)
    }
  }

  return (
    <div
      ref={containerRef}
      className="relative overflow-hidden rounded-xl shadow-2xl aspect-[2.3/1] sm:aspect-[3/1] md:aspect-[2.5/1] lg:aspect-[3.5/1] cursor-grab active:cursor-grabbing select-none"
      style={{ touchAction: 'pan-x' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeaveContainer}
    >
      {/* Image Slider */}
      <div
        ref={sliderRef}
        className="relative w-full h-full"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {bannerImages.map((image, index) => {
          const isActive = index === currentSlide
          const isPrev = index === (currentSlide - 1 + bannerImages.length) % bannerImages.length
          const isNext = index === (currentSlide + 1) % bannerImages.length

          let opacity = 0
          let transform = 'translateX(0px)'

          if (isActive) {
            opacity = 1
            if (isDragging) {
              transform = `translateX(${translateX}px)`
            }
          } else if (isDragging) {
            if (isPrev && dragProgress > 0) {
              opacity = Math.min(0.7, dragProgress * 2)
              transform = `translateX(${translateX - containerRef.current?.offsetWidth || 0}px)`
            } else if (isNext && dragProgress < 0) {
              opacity = Math.min(0.7, Math.abs(dragProgress) * 2)
              transform = `translateX(${translateX + (containerRef.current?.offsetWidth || 0)}px)`
            }
          }

          return (
            <div
              key={index}
              className="absolute inset-0"
              style={{
                opacity,
                transform,
                transition: isDragging ? 'none' : 'opacity 0.7s ease-in-out, transform 0.3s ease-out'
              }}
            >
              <Image
                src={image}
                alt={`بانر ${index + 1}`}
                fill
                className="object-cover pointer-events-none"
                priority={index === 0}
                quality={90}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 100vw, 100vw"
              />
            </div>
          )
        })}



        {/* Debug indicator */}
        {isDragging && (
          <div className="absolute top-4 left-4 bg-red-500 text-white px-2 py-1 rounded text-xs">
            Dragging: {translateX.toFixed(0)}px
          </div>
        )}

        {/* Enhanced Dots Indicator */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center space-x-3 bg-black/30 backdrop-blur-sm rounded-full px-4 py-2">
          {bannerImages.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`relative transition-all duration-300 ease-out focus:outline-none focus:ring-2 focus:ring-white/50 rounded-full ${
                index === currentSlide
                  ? "w-8 h-3 bg-white shadow-lg"
                  : "w-3 h-3 bg-white/40 hover:bg-white/60 hover:scale-110"
              }`}
              aria-label={`الانتقال إلى الشريحة ${index + 1}`}
              style={{
                borderRadius: index === currentSlide ? '12px' : '50%'
              }}
            >
              {index === currentSlide && (
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-80" />
              )}
            </button>
          ))}
        </div>

        {/* Loading indicator for smooth transitions */}
        {isDragging && (
          <div className="absolute inset-0 bg-black/10 pointer-events-none" />
        )}
      </div>
    </div>
  )
}
