"use client"

import Link from "next/link"

export default function PromoBanner() {
  return (
    <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 p-4 sm:p-6 md:p-8 shadow-2xl aspect-[2.3/1] sm:aspect-[3/1] md:aspect-auto">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-4 left-4 w-16 h-16 bg-yellow-400 rounded-full blur-xl"></div>
        <div className="absolute top-8 right-8 w-12 h-12 bg-cyan-400 rounded-full blur-lg"></div>
        <div className="absolute bottom-4 left-1/3 w-20 h-20 bg-pink-400 rounded-full blur-2xl"></div>
        <div className="absolute bottom-8 right-4 w-8 h-8 bg-green-400 rounded-full blur-md"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center h-full flex flex-col justify-center">
        <h2 className="text-lg sm:text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-1 sm:mb-2">
          ادع الأصدقاء للطلب
        </h2>
        <div className="flex items-center justify-center space-x-1 sm:space-x-2 space-x-reverse mb-2 sm:mb-4">
          <span className="text-sm sm:text-xl md:text-2xl lg:text-3xl font-bold text-white">احصل على</span>
          <span className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-yellow-300 bg-white/20 px-2 sm:px-3 py-1 rounded-lg">
            15%
          </span>
          <span className="text-sm sm:text-xl md:text-2xl lg:text-3xl font-bold text-white">لكل دعوة</span>
        </div>
        <p className="text-xs sm:text-sm md:text-base text-white/90 mb-3 sm:mb-6">
          يمكن تكرار كل دعوة
        </p>
        <Link
          href="/invite"
          className="inline-block bg-gradient-to-r from-cyan-400 to-blue-500 hover:from-cyan-500 hover:to-blue-600 text-white font-bold py-2 sm:py-3 px-4 sm:px-8 rounded-full text-sm sm:text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl"
        >
          اطلب الآن
        </Link>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-2 left-2 w-3 h-3 bg-white/30 rounded-full"></div>
      <div className="absolute top-6 left-6 w-2 h-2 bg-white/20 rounded-full"></div>
      <div className="absolute bottom-2 right-2 w-4 h-4 bg-white/25 rounded-full"></div>
      <div className="absolute bottom-6 right-6 w-1.5 h-1.5 bg-white/15 rounded-full"></div>
    </div>
  )
}
