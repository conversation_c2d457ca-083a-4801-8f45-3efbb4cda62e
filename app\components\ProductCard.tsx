import Image from "next/image"
import Link from "next/link"
import { Star, MessageSquare, Key, AlertTriangle } from "lucide-react"
import type { Product } from "../types"

interface ProductCardProps {
  product: Product
  userRole?: "admin" | "distributor" | "user"
}

export default function ProductCard({ product, userRole = "user" }: ProductCardProps) {
  const cheapestPackage = product.packages.reduce(
    (min, pkg) => (pkg.price < min.price ? pkg : min),
    product.packages[0],
  )

  return (
    <Link href={`/product/${product.slug}`}>
      <div className="bg-gray-800/90 backdrop-blur-sm rounded-xl overflow-hidden hover:bg-gray-700/90 transition-all duration-300 hover:scale-105 hover:shadow-xl border border-gray-700/30 h-full flex flex-col">
        {/* Product Image */}
        <div className="relative">
          <Image
            src={product.coverImage || "/logo.jpg"}
            alt={product.title}
            width={300}
            height={200}
            className="w-full h-32 sm:h-40 object-cover"
          />
          {/* Discount Badge - Top Right */}
          {cheapestPackage.discount && (
            <div className="absolute top-2 right-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white px-2 py-1 rounded-md text-xs font-bold shadow-lg transform -rotate-3">
              {cheapestPackage.discount}%OFF
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="p-3 flex-1 flex flex-col">
          {/* Product Title */}
          <h3 className="text-sm font-semibold mb-2 text-white line-clamp-2 flex-1">{product.title}</h3>

          {/* Rating and Reviews */}
          <div className="flex items-center justify-center space-x-1 space-x-reverse mt-auto">
            <div className="flex items-center space-x-1 space-x-reverse">
              <Star className="w-3 h-3 text-yellow-400 fill-current" />
              <span className="text-xs text-white font-semibold">{product.rating}</span>
            </div>
            <span className="text-xs text-gray-400">{product.commentCount} تقييم</span>
          </div>
        </div>
      </div>
    </Link>
  )
}
