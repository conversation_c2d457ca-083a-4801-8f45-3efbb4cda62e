import Image from "next/image"
import Link from "next/link"
import { Star } from "lucide-react"
import type { Product } from "../types"

interface ProductCardProps {
  product: Product
  userRole?: "admin" | "distributor" | "user"
}

export default function ProductCard({ product, userRole = "user" }: ProductCardProps) {
  const cheapestPackage = product.packages.reduce(
    (min, pkg) => (pkg.price < min.price ? pkg : min),
    product.packages[0],
  )

  return (
    <Link href={`/product/${product.slug}`}>
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl overflow-hidden hover:bg-gray-700/60 transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl border border-gray-700/20 h-full flex flex-col group">
        {/* Product Image */}
        <div className="relative aspect-[4/3] overflow-hidden">
          <Image
            src={product.coverImage || "/logo.jpg"}
            alt={product.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
          />

          {/* Modern Discount Badge - Top Right */}
          {cheapestPackage.discount && (
            <div className="absolute top-3 right-3 z-10">
              <div className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-2.5 py-1 rounded-full text-xs font-bold shadow-lg">
                -{cheapestPackage.discount}%
              </div>
            </div>
          )}

          {/* Gradient Overlay for better text readability */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
        </div>

        {/* Product Info */}
        <div className="p-3 flex-1 flex flex-col space-y-2">
          {/* Product Title */}
          <h3 className="text-sm font-bold text-white line-clamp-2 leading-tight flex-1 group-hover:text-purple-300 transition-colors">
            {product.title}
          </h3>

          {/* Rating and Reviews */}
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center space-x-1 space-x-reverse">
              <Star className="w-3.5 h-3.5 text-yellow-400 fill-current" />
              <span className="text-yellow-400 font-bold">{product.rating}</span>
            </div>
            <span className="text-gray-400 font-medium">
              {product.commentCount.toLocaleString()} تقييم
            </span>
          </div>

          {/* Category Badge */}
          <div className="flex justify-start">
            <span className="inline-block bg-purple-500/20 text-purple-300 px-2 py-1 rounded-md text-xs font-medium">
              {product.category}
            </span>
          </div>
        </div>
      </div>
    </Link>
  )
}
